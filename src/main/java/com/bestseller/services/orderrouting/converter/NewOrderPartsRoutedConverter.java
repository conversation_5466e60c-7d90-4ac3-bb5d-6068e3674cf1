package com.bestseller.services.orderrouting.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.CustomerInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class NewOrderPartsRoutedConverter implements Converter<OrderModel, List<OrderPartsRouted>> {

    @Override
    public List<OrderPartsRouted> convert(OrderModel source) {
        Map<Integer, String> orderPartNumbers = new HashMap<>();

        source.getOrderLineQuantities().stream()
            .filter(this::isSubmittable)
            .forEach(line -> orderPartNumbers.putIfAbsent(line.getOrderPartNumber(), line.getFulfillmentNode()));

        return source.getOrderLineQuantities().stream()
            .filter(this::isSubmittable)
            .collect(
                Collectors.groupingBy(
                    OrderLineQuantityModel::getOrderPartNumber,
                    TreeMap::new,
                    Collectors.toList()
                ))
            .entrySet()
            .stream()
            .map(orderPartMapper(source, orderPartNumbers))
            .collect(Collectors.toList());
    }

    private boolean isSubmittable(final OrderLineQuantityModel olqm) {
        return olqm.getStatus().equals(OrderLineQuantityStatus.ROUTING)
            || olqm.getStatus().equals(OrderLineQuantityStatus.RESUBMIT);
    }

    private Function<Map.Entry<Integer, List<OrderLineQuantityModel>>, OrderPartsRouted> orderPartMapper(
        OrderModel orderModel, Map<Integer, String> orderPartNumbers) {

        return entry -> new OrderPartsRouted()
            .withOrderId(orderModel.getOrderId())
            .withCustomerInformation(
                new CustomerInformation()
                    .withEmail()
            )
            .withFulfillmentNode(orderPartNumbers.get(entry.getKey()))
            .withOrderPartNumber(entry.getKey())
            .withTotalOrderParts(orderPartNumbers.size());
    }

}
