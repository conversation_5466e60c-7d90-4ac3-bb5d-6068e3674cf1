package com.bestseller.services.orderrouting.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.common.AdditionalInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.CustomerInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.Payment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.ShippingInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.services.orderrouting.model.AdditionalOrderInformationModel;
import com.bestseller.services.orderrouting.model.AddressModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.model.PaymentModel;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.junit.Before;
import org.junit.Test;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static java.util.Arrays.asList;
import static org.assertj.core.api.Assertions.assertThat;

public class OrderPartsRoutedConverterTest {
    private static final String WAREHOUSE_1 = "WAREHOUSE-1";
    private static final String WAREHOUSE_2 = "WAREHOUSE-2";
    private static final int ORDER_PART_NUMBER_1 = 1;
    private static final int ORDER_PART_NUMBER_2 = 2;
    private static final String ISO_STORE_ID = "781263";

    private final OrderPartsRoutedConverter converter = new OrderPartsRoutedConverter();

    private OrderModel orderModel;
    private OrderLineQuantityModel orderLine1;
    private OrderLineQuantityModel orderLine2;
    private OrderLineQuantityModel orderLine3;

    @Before
    public void setUp() {
        orderModel = OrderModelGenerator.createTestOrderModel();
        orderModel.setIsoStoreId(ISO_STORE_ID);
        orderModel.setPayments(OrderModelGenerator.createTwoPayments(orderModel));
        orderModel.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(orderModel));
        orderModel.getOrderLineQuantities().forEach(q -> q.setStatus(OrderLineQuantityStatus.ROUTING));
        orderLine1 = orderModel.getOrderLineQuantities().get(0);
        orderLine2 = orderModel.getOrderLineQuantities().get(1);
        orderLine3 = orderModel.getOrderLineQuantities().get(2);

        orderLine1.setFulfillmentNode(WAREHOUSE_1);
        orderLine1.setOrderPartNumber(1);
        orderLine2.setFulfillmentNode(WAREHOUSE_1);
        orderLine2.setOrderPartNumber(1);
        orderLine3.setFulfillmentNode(WAREHOUSE_2);
        orderLine3.setOrderPartNumber(2);
    }

    @Test
    public void convert_noRoutingLinesGiven_noOrderPartMessages() {
        // arrange
        orderModel.getOrderLineQuantities().forEach(o -> o.setStatus(OrderLineQuantityStatus.ROUTED));

        // act
        List<OrderPartsRouted> orderParts = converter.convert(orderModel);

        // assert
        assertThat(orderParts).isEmpty();
    }

    @Test
    public void convert_routingAndRoutedLinesGiven_orderPartMessagesCreated() {
        // arrange
        OrderLineQuantityModel orderLinePlaced = new OrderLineQuantityModel();
        orderLinePlaced.setStatus(OrderLineQuantityStatus.ROUTED);
        orderLinePlaced.setOrderPartNumber(1);
        orderModel.getOrderLineQuantities().add(orderLinePlaced);

        // act
        List<OrderPartsRouted> orderParts = converter.convert(orderModel);

        // assert
        assertThat(orderParts).hasSize(2);
        assertOrderPartMessage(orderParts.get(0), WAREHOUSE_1, ORDER_PART_NUMBER_1, orderLine1, orderLine2);
        assertOrderPartMessage(orderParts.get(1), WAREHOUSE_2, ORDER_PART_NUMBER_2, orderLine3);
    }

    @Test
    public void convert_resubmitLinesGiven_orderPartMessagesCreated() {
        // arrange
        OrderLineQuantityModel orderLinePlaced = new OrderLineQuantityModel();
        orderLinePlaced.setStatus(OrderLineQuantityStatus.RESUBMIT);
        orderLinePlaced.setOrderPartNumber(1);
        orderModel.getOrderLineQuantities().add(orderLinePlaced);

        // act
        List<OrderPartsRouted> orderParts = converter.convert(orderModel);

        // assert
        assertThat(orderParts).hasSize(2);
        assertOrderPartMessage(orderParts.get(0), WAREHOUSE_1, ORDER_PART_NUMBER_1, orderLine1, orderLine2,
            orderLinePlaced);
        assertOrderPartMessage(orderParts.get(1), WAREHOUSE_2, ORDER_PART_NUMBER_2, orderLine3);
    }

    @Test
    public void convert_routingLinesGiven_orderPartMessagesCreated() {
        // arrange

        // act
        List<OrderPartsRouted> orderParts = converter.convert(orderModel);

        // assert
        assertThat(orderParts).hasSize(2);
        assertOrderPartMessage(orderParts.get(0), WAREHOUSE_1, ORDER_PART_NUMBER_1, orderLine1, orderLine2);
        assertOrderPartMessage(orderParts.get(1), WAREHOUSE_2, ORDER_PART_NUMBER_2, orderLine3);
    }

    @Test
    public void convert_singleLine_onePartWithCorrectNumbers() {
        // arrange
        OrderLineQuantityModel line = OrderModelGenerator.createOneTestOrderLine(orderModel);
        line.setStatus(OrderLineQuantityStatus.ROUTING);
        line.setOrderPartNumber(1);
        orderModel.setOrderLineQuantities(asList(line));

        // act
        List<OrderPartsRouted> orderParts = converter.convert(orderModel);

        // assert
        assertThat(orderParts).hasSize(1);
        assertThat(orderParts.get(0).getTotalOrderParts()).isEqualTo(1);
        assertThat(orderParts.get(0).getOrderPartNumber()).isEqualTo(ORDER_PART_NUMBER_1);
    }

    @Test
    public void convert_nullInstancesGiven_nullZonedDateTimesSet() {
        // arrange
        orderModel.setPlacedDate(null);
        orderModel.setOrderCreationDate(null);

        // act
        List<OrderPartsRouted> orderParts = converter.convert(orderModel);

        // assert
        assertThat(orderParts).hasSize(2);
        orderParts.stream()
            .map(OrderPartsRouted::getPlacedDate)
            .forEach(placedDate -> assertThat(placedDate).isNull());
        orderParts
            .stream()
            .map(OrderPartsRouted::getOrderDetails)
            .map(OrderDetails::getOrderCreationDate)
            .forEach(orderCreationDate -> assertThat(orderCreationDate).isNull());
    }

    @SuppressWarnings("PMD.NcssCount")
    private void assertOrderPartMessage(OrderPartsRouted message,
                                        String expectedFulfillmentNode,
                                        Integer expectedOrderPartNumber,
                                        OrderLineQuantityModel... lines) {
        OrderPartsRouted expected = new OrderPartsRouted()
            .withOrderId(orderModel.getOrderId())
            .withFulfillmentNode(expectedFulfillmentNode)
            .withOrderPartNumber(expectedOrderPartNumber)
            .withTotalOrderParts(2)
            .withMarketPlace(orderModel.getMarketPlace())
            .withStore(orderModel.getStore())
            .withChannel(orderModel.getChannel())
            .withBrand(orderModel.getBrand())
            .withActionCode(orderModel.getActionCode())
            .withIsTest(orderModel.getIsTest())
            .withPlacedDate(convertInstantToZonedDateTime(orderModel.getPlacedDate()))
            .withOrderDetails(new OrderDetails()
                .withCarrier(orderModel.getCarrier())
                .withCarrierVariant(orderModel.getCarrierVariant())
                .withExternalOrderNumber(orderModel.getExternalOrderNumber())
                .withOrderCreationDate(convertInstantToZonedDateTime(orderModel.getOrderCreationDate()))
                .withOrderType(orderModel.getOrderType())
                .withOrderValue(orderModel.getOrderValue())
                .withCurrency(orderModel.getCurrency())
                .withCheckout(orderModel.getCheckout())
                .withShippingFees(orderModel.getShippingFees())
                .withShippingFeesCancelled(orderModel.getShippingFeesCancelled())
                .withShippingFeesTaxPercentage(orderModel.getShippingFeesTaxPercentage())
                .withIsoStoreId(orderModel.getIsoStoreId())
                .withShippingMethod(orderModel.getShippingMethod())
            )
            .withCustomerInformation(
                new CustomerInformation()
                    .withEmail(orderModel.getCustomerEmail())
                    .withCustomerId(orderModel.getCustomerId())
                    .withCustomerLocale(orderModel.getCustomerLocale())
                    .withExternalCustomerNumber(orderModel.getExternalCustomerNumber())
                    .withBillingAddress(convertAddress(orderModel.getBillingAddress()))
            )
            .withShippingInformation(
                new ShippingInformation()
                    .withShippingAddress(convertAddress(orderModel.getShippingAddress()))
                    .withParcelLocker(orderModel.getParcelLocker())
                    .withAdditionalInformation(convertAdditionalInformation(orderModel.getAdditionalOrderInformation()))
            )
            .withOrderLines(convertOrderLines(lines))
            .withPayments(convertPayments(orderModel.getPayments()));

        assertThat(message)
            .usingRecursiveComparison()
            .isEqualTo(expected);
    }

    private ZonedDateTime convertInstantToZonedDateTime(Instant instant) {
        return instant != null ? instant.atZone(ZoneId.of("UTC")) : null;
    }

    private Address convertAddress(AddressModel addressModel) {
        return new Address()
            .withAddressLine1(addressModel.getAddressLine1())
            .withAddressLine2(addressModel.getAddressLine2())
            .withAddressLine3(addressModel.getAddressLine3())
            .withCity(addressModel.getCity())
            .withCountry(addressModel.getCountry())
            .withState(addressModel.getState())
            .withFirstName(addressModel.getFirstName())
            .withLastName(addressModel.getLastName())
            .withHouseNumber(addressModel.getHouseNumber())
            .withHouseNumberExtended(addressModel.getHouseNumberExtended())
            .withPhoneNumber(addressModel.getPhoneNumber())
            .withZipcode(addressModel.getZipcode());
    }

    private Set<AdditionalInformation> convertAdditionalInformation(
        List<AdditionalOrderInformationModel> additionalOrderInformationModels
    ) {
        return additionalOrderInformationModels.stream()
            .map(info -> new AdditionalInformation()
                .withKey(info.getKey())
                .withValue(info.getValue())
            )
            .collect(Collectors.toSet());
    }

    private List<OrderLine> convertOrderLines(OrderLineQuantityModel... lines) {
        return Arrays.stream(lines)
            .map(line -> new OrderLine()
                .withEan(line.getEan())
                .withProductName(line.getProductName())
                .withLineNumber(line.getLineNumber())
                .withQuantity(line.getQuantity())
                .withRetailPrice(line.getRetailPrice())
                .withDiscountValue(line.getDiscountValue())
                .withTaxPercentage(line.getTaxPercentage())
                .withIsGiftItem(line.getIsGiftItem())
                .withPartnerReference(line.getPartnerReference())
                .withPromotionId(line.getOrderId())
            )
            .collect(Collectors.toList());
    }

    private List<Payment> convertPayments(List<PaymentModel> paymentModels) {
        return paymentModels.stream()
            .map(payment -> new Payment()
                .withName(payment.getName())
                .withSubMethod(payment.getSubMethod())
            )
            .collect(Collectors.toList());
    }

}
